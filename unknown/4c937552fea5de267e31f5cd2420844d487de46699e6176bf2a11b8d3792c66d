import React from "react";
import { baseHTML } from "@/lib/template/prescritpion";
import { type ActivityDetails } from "@/lib/ai/tools/schema/activity-details";
import { Button } from "@/components/ui/button";
import { FileText } from "lucide-react";

interface PreviewPrescriptionPDFProps {
  activityDetails: ActivityDetails;
}

const PreviewPrescriptionPDF: React.FC<PreviewPrescriptionPDFProps> = ({
  activityDetails,
}) => {
  console.log("PreviewPrescriptionPDF", activityDetails);
  const handlePreview = () => {
    // Generate the HTML content
    const htmlContent = baseHTML({ activityDetails });

    // Open a new tab
    const newWindow = window.open("", "_blank");

    // Safety check in case popup is blocked
    if (!newWindow) {
      alert("Please allow popups to preview the prescription");
      return;
    }

    // Write the HTML content to the new tab
    newWindow.document.write(htmlContent);
    newWindow.document.close();

    // Optional: Add print functionality
    // newWindow.print();
  };

  return (
    <div className="mt-4">
      <p className="text-gray-600 mb-4">
        This is a preview of the prescription based on the details you provided
        Click the button below to preview the prescription in a new tab.
      </p>
      <Button
        onClick={handlePreview}
        className="flex items-center gap-2 bg-purple-700 hover:bg-purple-800 text-white font-medium py-2 px-4 rounded-md transition-colors"
      >
        <FileText className="h-5 w-5" />
        Preview Prescription
      </Button>
    </div>
  );
};

export default PreviewPrescriptionPDF;
