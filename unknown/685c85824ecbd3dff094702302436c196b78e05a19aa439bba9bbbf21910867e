import { tool } from "ai";
import { z } from "zod";
import { activityDetailsSchema } from "./schema/activity-details";


export const generatePrescriptionPDF = tool({
  description:
    "If user provided bovine details, observation, diagnosis and medicines then Generate prescription pdf",
  parameters: activityDetailsSchema,
  execute: async (params) => {
    console.log("Called generate prescription pdf", params);
    return params;
  },
});

export const confirmToDownloadPrescriptionPDF = tool({
  description: "Ask confirmation for download the prescription pdf",
  parameters: z.object({}),
});


export const downloadPrescriptionPDF = tool({
  description: "Download the prescription pdf",
  parameters: z.object({
    farmerId: z.string().describe("The url of the prescription pdf"),
    careCalendarId: z.string().describe("The url of the prescription pdf"),
  }),
  execute: async (params) => {
    console.log("Called download prescription pdf", params);
    return params;
  }
});

