export async function POST(req: Request) {
  try {
    const { text } = await req.json();

    if (!text) {
      return Response.json({ error: "Text is required" }, { status: 400 });
    }

    // We don't actually process the text here since Chrome's Web Speech API
    // is client-side only. We just validate the request and return success.
    // The actual TTS will happen in the browser.

    return Response.json({
      success: true,
      text: text,
    });
  } catch (error) {
    console.error("TTS error:", error);
    return Response.json(
      {
        error:
          "Failed to process text-to-speech request: " +
          (error instanceof Error ? error.message : "Unknown error"),
      },
      { status: 500 }
    );
  }
}
