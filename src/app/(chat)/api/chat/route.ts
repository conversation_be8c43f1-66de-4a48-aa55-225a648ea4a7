import { createDataStream, smoothStream, streamText } from "ai";
import { systemPrompt } from "@/lib/ai/prompts";
import { generateUUID } from "@/lib/utils";
import { isProductionEnvironment } from "@/lib/constants";
import { myProvider } from "@/lib/ai/providers";
import { postRequestBodySchema, type PostRequestBody } from "./schema";
import { generatePrescription } from "@/lib/ai/tools/generatePrescription";
import { getComplaintDetailsById } from "@/lib/ai/tools/getComplaintDetailsById";
import { checkDiagnosis } from "@/lib/ai/tools/checkDiagnosis";
import { checkMedicine } from "@/lib/ai/tools/checkMedicine";
import { RequestOptions } from "@/types/RequestOption";

export const maxDuration = 60;

export async function POST(request: Request) {
  let requestBody: PostRequestBody;
  let requestHeaders: Headers;
  let authToken: string;
  let baseURL: string;

  try {
    requestHeaders = request.headers;
    authToken =
      requestHeaders.get("authtoken")?.trim() || process.env.Token || "";
    baseURL =
      requestHeaders.get("baseurl")?.trim() ||
      process.env.BASE_URL?.trim() ||
      "";
  } catch (_) {
    return new Response("Invalid request headers", { status: 400 });
  }

  const options: RequestOptions = {
    baseUrl: baseURL,
    headers: {
      "Content-Type": "application/json",
      Token: authToken,
    },
  };

  try {
    requestBody = await request.json();
    // requestBody = postRequestBodySchema.parse(json);
  } catch (_) {
    return new Response("Invalid request body", { status: 400 });
  }

  try {
    const { id, message, selectedChatModel, messages } = requestBody;

    // Create a simple stream without database operations
    // Approach 2: Enhanced error handling with retry state

    const stream = createDataStream({
      execute: (dataStream) => {
        let retryCount = 0;
        const maxFunctionRetries = 3;

        const executeWithRetry = async () => {
          try {
            // Get enhanced system prompt for retries
            const baseSystemPrompt = systemPrompt({ selectedChatModel });
            const enhancedSystemPrompt =
              retryCount > 0
                ? baseSystemPrompt +
                  `\n\nIMPORTANT: When calling functions, ensure all required parameters are provided correctly. This is retry attempt ${retryCount}.`
                : baseSystemPrompt;

            const result = streamText({
              model: myProvider.languageModel("chat-model"),
              system: enhancedSystemPrompt,
              messages,
              maxSteps: 15,
              maxRetries: 3,
              experimental_activeTools:
                selectedChatModel === "chat-model-reasoning"
                  ? []
                  : [
                      "getComplaintDetailsById",
                      "checkDiagnosis",
                      "checkMedicine",
                      "generatePrescription",
                    ],
              experimental_transform: smoothStream({ chunking: "word" }),
              experimental_generateMessageId: generateUUID,
              tools: {
                getComplaintDetailsById: getComplaintDetailsById(options),
                checkDiagnosis: checkDiagnosis(options),
                checkMedicine: checkMedicine(options),
                generatePrescription: generatePrescription(options),
              },
              onError: async ({ error }: { error: any }) => {
                console.error(
                  `Error from streamText (retry ${retryCount}):`,
                  error
                );

                // Check if this is a retryable function call error
                const isRetryableError =
                  error.type === "invalid_request_error" &&
                  error.message.includes("Failed to call a function");

                if (isRetryableError && retryCount < maxFunctionRetries) {
                  retryCount++;
                  const delay = 1000 * retryCount; // Linear backoff

                  console.log(
                    `Retrying function call in ${delay}ms... (${retryCount}/${maxFunctionRetries})`
                  );

                  // Wait before retry
                  await new Promise((resolve) => setTimeout(resolve, delay));

                  // Retry with enhanced prompt
                  return executeWithRetry();
                }

                // If not retryable or max retries reached
                throw error;
              },
            });

            result.consumeStream();
            result.mergeIntoDataStream(dataStream, {
              sendReasoning: true,
            });
          } catch (error) {
            console.error("Failed after all retries:", error);
            // Just throw the error, let the outer onError handle it
            throw error;
          }
        };

        executeWithRetry();
      },
      onError: (error) => {
        console.log("DataStream error:", error);
        return `Oops, an error occurred after multiple attempts! Please try again.`;
      },
    });

    return new Response(stream);
  } catch (error) {
    console.error("Error in chat API:", error);
    return new Response("An error occurred while processing your request!", {
      status: 500,
    });
  }
}

export async function GET(request: Request) {
  // Return an empty response for GET requests
  // In a real app, this would retrieve chat history
  return new Response(null, { status: 204 });
}

export async function DELETE(request: Request) {
  // Return a success response for DELETE requests
  // In a real app, this would delete a chat
  return new Response(null, { status: 200 });
}
