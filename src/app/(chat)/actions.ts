"use server";

import { generateText, type UIMessage } from "ai";
import { cookies } from "next/headers";
import {
  deleteMessagesByChatIdAfterTimestamp,
  getMessageById,
  updateChatVisiblityById,
} from "@/lib/db/queries";
import type { VisibilityType } from "@/components/visibility-selector";
import { myProvider } from "@/lib/ai/providers";

export async function saveChatModelAsCookie(model: string) {
  const cookieStore = await cookies();
  cookieStore.set("chat-model", model);
}

export async function generateTitleFromUserMessage({
  message,
}: {
  message: UIMessage;
}) {
  const { text: title } = await generateText({
    model: myProvider.languageModel("title-model"),
    system: `\n
    - you will generate a short title based on the first message a user begins a conversation with
    - ensure it is not more than 80 characters long
    - the title should be a summary of the user's message
    - do not use quotes or colons`,
    prompt: JSON.stringify(message),
  });

  return title;
}

export async function deleteTrailingMessages({ id }: { id: string }) {
  const [message] = await getMessageById({ id });

  await deleteMessagesByChatIdAfterTimestamp({
    chatId: message.chatId,
    timestamp: message.createdAt,
  });
}

export async function updateChatVisibility({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: VisibilityType;
}) {
  await updateChatVisiblityById({ chatId, visibility });
}

export async function summarizeResponseFromLLM({
  message,
}: {
  message: string;
}) {
  const { text } = await generateText({
    model: myProvider.languageModel("summarize-model"),
    system: `\n
# TTS Rephrasing Agent Prompt

You are a rephrasing agent for a text-to-speech system with UI display.
*** DO NOT SUGGEST MEDICAL DIAGNOSES OR MEDICATIONS ***

## Core Task
Rephrase input messages to be under 100 characters while preserving original meaning and intent.

## Guidelines
- Keep the original meaning clear and intact
- Optimize for quick readability in user interfaces
- Use concise, natural language
- Maintain the same tone and urgency level
- If the original is a question, keep it as a question and place it first
- Do not add, infer, or omit important details—only compress and reword
- Avoid including numbers, UUID or ID in the rephrased text
- Do not ask users to type or input anything
- Avoid reading out lists just ask users to "choose one" from options
- Spell out aberrations such as PDF, etc.. so that the TTS can avoid using constants.
- Omit brackets, numbers etc.. remember you are having a natural language conversion with a user.

## Restrictions
- **NEVER suggest medical diagnoses or medications**
- Do not add information not present in the original
- Do not change the message type (statement to question, etc.)

## Output Format
Provide only the rephrased text, no explanations or additional commentary.`,

    prompt: message,
  });
  console.log("summarized text", text);
  return text;
}
