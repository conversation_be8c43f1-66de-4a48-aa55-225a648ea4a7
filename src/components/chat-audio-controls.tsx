"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "./ui/button";
import { StopIcon, MicrophoneIcon, HeadphonesIcon } from "./icons";
import { useSpeechSynthesis } from "@/hooks/use-speech-synthesis";
import { useScrollToBottom } from "@/hooks/use-scroll-to-bottom";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

interface ChatAudioControlsProps {
  text: string;
  isListening: boolean;
  isProcessing: boolean;
  onStartListening: () => void;
  onStopListening: () => void;
  onClose: () => void;
  className?: string;
  status?: "ready" | "submitted" | "streaming" | "error";
}

export function ChatAudioControls({
  text,
  isListening,
  isProcessing,
  onStartListening,
  onStopListening,
  onClose,
  className,
  status = "ready",
}: ChatAudioControlsProps) {
  const {
    speak,
    speakWithAPI,
    cancel,
    pause,
    resume,
    state,
    isSupported,
    setVolume,
    volume,
  } = useSpeechSynthesis({
    onEnd: () => {
      // Auto-start listening when speech ends
      if (!isListening && !isProcessing) {
        onStartListening();
      }
    },
    onError: (error) => {
      console.error("Speech synthesis error:", error);
      toast.error(
        `Speech synthesis error: ${
          error instanceof Error ? error.message : error.error
        }`
      );
    },
  });

  // Add scroll functionality
  const { scrollToBottom } = useScrollToBottom();

  // Auto-scroll when status changes to submitted (when a message is sent)
  useEffect(() => {
    if (status === "submitted") {
      scrollToBottom();
    }
  }, [status, scrollToBottom]);

  // Auto-scroll when starting to listen (when user starts speaking)
  useEffect(() => {
    if (isListening) {
      scrollToBottom();
    }
  }, [isListening, scrollToBottom]);

  // Auto-scroll when AI starts speaking
  useEffect(() => {
    if (state === "speaking") {
      scrollToBottom();
    }
  }, [state, scrollToBottom]);

  const [autoPlay, setAutoPlay] = useState(true);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const microphoneStreamRef = useRef<MediaStream | null>(null);
  const sourceNodeRef = useRef<MediaStreamAudioSourceNode | null>(null);

  // Auto-play text when it changes
  useEffect(() => {
    if (
      text &&
      autoPlay &&
      !isListening &&
      !isProcessing &&
      state !== "speaking"
    ) {
      console.log("Auto-playing text:", text);

      // Add a small delay to ensure the TTS API is ready
      const timer = setTimeout(async () => {
        try {
          await speakWithAPI(text);
        } catch (error) {
          console.error("Failed to speak with API:", error);
        }
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [text, autoPlay, isListening, isProcessing, state, speak]);

  // Debug text content
  useEffect(() => {
    if (text) {
      console.log("Text for TTS:", text);
      console.log("Text length:", text.length);
    }
  }, [text]);

  // Initialize audio context and analyzer
  useEffect(() => {
    if (typeof window !== "undefined") {
      audioContextRef.current = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;
    }

    return () => {
      if (
        audioContextRef.current &&
        audioContextRef.current.state !== "closed"
      ) {
        audioContextRef.current.close();
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (microphoneStreamRef.current) {
        microphoneStreamRef.current
          .getTracks()
          .forEach((track) => track.stop());
      }
    };
  }, []);

  // Setup microphone stream when listening
  useEffect(() => {
    if (isListening && !microphoneStreamRef.current) {
      const setupMicrophone = async () => {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
          });
          microphoneStreamRef.current = stream;

          if (audioContextRef.current && analyserRef.current) {
            sourceNodeRef.current =
              audioContextRef.current.createMediaStreamSource(stream);
            sourceNodeRef.current.connect(analyserRef.current);
            startVisualizer();
          }
        } catch (error) {
          console.error("Error accessing microphone:", error);
        }
      };

      setupMicrophone();
    } else if (!isListening && microphoneStreamRef.current) {
      // Clean up microphone stream when not listening
      microphoneStreamRef.current.getTracks().forEach((track) => track.stop());
      microphoneStreamRef.current = null;

      if (sourceNodeRef.current) {
        sourceNodeRef.current.disconnect();
        sourceNodeRef.current = null;
      }
    }
  }, [isListening]);

  // Start/stop visualizer based on speaking or listening state
  useEffect(() => {
    if ((state === "speaking" || isListening) && canvasRef.current) {
      startVisualizer();
    } else {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }

      // Draw empty visualizer
      if (canvasRef.current) {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext("2d");
        if (ctx) {
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          drawEmptyBars(ctx, canvas.width, canvas.height);
        }
      }
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [state, isListening]);

  // Draw empty equalizer bars
  const drawEmptyBars = (
    ctx: CanvasRenderingContext2D,
    width: number,
    height: number
  ) => {
    const barCount = 100;
    const barWidth = width / barCount;
    const barGap = 2;

    ctx.fillStyle = "#e2e8f0"; // Light gray for inactive bars

    for (let i = 0; i < barCount; i++) {
      const x = i * barWidth;
      const barHeight = 3; // Minimal height for inactive bars
      ctx.fillRect(
        x + barGap / 2,
        height - barHeight,
        barWidth - barGap,
        barHeight
      );
    }
  };

  // Start the visualizer animation
  const startVisualizer = () => {
    if (!canvasRef.current || !analyserRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const analyser = analyserRef.current;
    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const draw = () => {
      animationRef.current = requestAnimationFrame(draw);

      analyser.getByteFrequencyData(dataArray);

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      const barCount = 100; // Number of bars to display
      const barWidth = canvas.width / barCount;
      const barGap = 2;

      // Generate some fake data if we're speaking but don't have real audio data
      const data =
        state === "speaking" && !isListening
          ? Array.from({ length: barCount }, () => Math.random() * 100 + 50)
          : Array.from(dataArray.slice(0, barCount));

      // Draw the bars
      for (let i = 0; i < barCount; i++) {
        const value = data[i];
        const percent = value / 255;
        const barHeight = percent * canvas.height;

        // Create gradient based on height
        let color;
        if (isListening) {
          color = `rgb(239, 68, 68, ${0.7 + percent * 0.3})`; // Red for listening
        } else {
          // Blue gradient for speaking
          color = `rgb(59, 130, 246, ${0.7 + percent * 0.3})`;
        }

        ctx.fillStyle = color;
        ctx.fillRect(
          i * barWidth + barGap / 2,
          canvas.height - barHeight,
          barWidth - barGap,
          barHeight
        );
      }
    };

    draw();
  };

  // Handle speech controls
  const handlePlay = async () => {
    if (state === "paused") {
      resume();
    } else {
      console.log("Playing text:", text);
      try {
        await speak(text);
      } catch (error) {
        console.error("Failed to speak:", error);
      }
    }
  };

  const handlePause = () => {
    pause();
  };

  const handleStop = () => {
    cancel();
  };

  const closeAudio = () => {
    handleStop();
    onClose();
  };

  // Handle microphone controls
  const handleMicrophoneClick = () => {
    if (isListening) {
      onStopListening();
    } else {
      // Cancel any ongoing speech before starting to listen
      if (state === "speaking" || state === "paused") {
        cancel();
      }
      onStartListening();
    }
  };

  // Get button style based on state
  const getMicButtonStyle = () => {
    if (isListening) {
      return "bg-red-500 hover:bg-red-600 text-white";
    } else if (isProcessing) {
      return "bg-purple-500 hover:bg-purple-600 text-white";
    }
    return "bg-purple-500 hover:bg-purple-600 text-white";
  };

  if (!isSupported) {
    return (
      <div className={cn("flex flex-col items-center gap-4 p-4", className)}>
        <div className="text-red-500">
          Speech synthesis is not supported in this browser.
        </div>
        <Button onClick={closeAudio} variant="outline">
          Close Audio Mode
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col items-center gap-4", className)}>
      <div className="w-full rounded-lg border border-zinc-700 p-4">
        {/* Header */}
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <HeadphonesIcon size={16} />
            <span className="text-sm font-medium">Audio Mode</span>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => setAutoPlay(!autoPlay)}
              variant={autoPlay ? "default" : "outline"}
              size="sm"
              className="h-8 px-2"
            >
              {autoPlay ? "Auto-play On" : "Auto-play Off"}
            </Button>
            <Button
              onClick={closeAudio}
              variant="ghost"
              size="sm"
              className="h-8 px-2"
            >
              Close
            </Button>
          </div>
        </div>

        {/* Equalizer visualization */}
        <div className="w-full h-16 flex items-center justify-center">
          <canvas
            ref={canvasRef}
            className="w-full h-12 rounded-md"
            width={300}
            height={48}
          />
        </div>

        {/* Main controls */}
        <div className="flex items-center justify-center gap-4">
          {/* Microphone button */}
          <Button
            data-testid="chat-audio-microphone-button"
            className={`rounded-full p-3 h-16 w-16 animate-pulse ${getMicButtonStyle()}`}
            onClick={handleMicrophoneClick}
            disabled={isProcessing}
            variant={isListening ? "default" : "outline"}
            aria-label={isListening ? "Stop listening" : "Start listening"}
          >
            {isListening ? (
              <StopIcon size={18} />
            ) : (
              <MicrophoneIcon size={18} />
            )}
          </Button>

          {/* Status indicator */}
          <div className="flex flex-col items-center">
            {isListening && (
              <div className="text-sm text-red-500 animate-pulse">
                Listening...
              </div>
            )}
            {isProcessing && (
              <div className="text-sm text-purple-500">Processing...</div>
            )}
            {state === "speaking" && (
              <div className="text-sm text-blue-500 animate-pulse">
                Speaking...
              </div>
            )}
          </div>

          {/* TTS controls */}
          {/* <div className="flex items-center gap-2">
          {state === "speaking" ? (
            <Button
              onClick={handlePause}
              variant="outline"
              size="icon"
              className="rounded-full h-10 w-10"
              aria-label="Pause speech"
            >
              <PauseIcon size={16} />
            </Button>
          ) : (
            <Button
              onClick={handlePlay}
              variant="outline"
              size="icon"
              className="rounded-full h-10 w-10"
              aria-label="Play speech"
              disabled={!text || isProcessing}
            >
              <PlayIcon size={16} />
            </Button>
          )}
          <Button
            onClick={handleStop}
            variant="outline"
            size="icon"
            className="rounded-full h-10 w-10"
            aria-label="Stop speech"
            disabled={state !== "speaking" && state !== "paused"}
          >
            <StopIcon size={16} />
          </Button>
        </div> */}
        </div>

        {/* Volume and speed controls */}
        <div className="flex flex-row gap-4 w-full max-w-md">
          {/* Volume control */}
          {/* <div className="flex items-center gap-2 flex-1">
          <VolumeIcon size={14} />
          <Slider
            value={[volume * 100]}
            min={0}
            max={100}
            step={1}
            onValueChange={(value) => setVolume(value[0] / 100)}
            aria-label="Volume"
          />
          <Volume2Icon size={14} />
        </div> */}

          {/* Speed control */}
          {/* <div className="flex items-center gap-2 flex-1">
          <span className="text-xs">Slow</span>
          <Slider
            value={[rate * 50]}
            min={25}
            max={200}
            step={25}
            onValueChange={(value) => setRate(value[0] / 100)}
            aria-label="Speech rate"
          />
          <span className="text-xs">Fast</span>
        </div> */}
        </div>
      </div>
    </div>
  );
}
