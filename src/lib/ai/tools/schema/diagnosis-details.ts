import { z } from "zod";

export const diagnosisDetailsSchema = z.object({
  activity_id: z
    .string()
    .describe(
      "activity id is care calendar id  . get diagnosis id array from the user input "
    )
    .min(1, "activity_id is required"),
  diagnosis: z
    .array(
      z.object({
        id: z
          .union([z.string().min(1, "diagnosis id is required"), z.number()])
          .describe("diagnosis id"),
        name: z
          .string()
          .min(1, "diagnosis name is required")
          .describe("diagnosis name"),
      })
    )
    .min(1, "diagnosis is required")
    .describe("diagnosis is required"),
});
