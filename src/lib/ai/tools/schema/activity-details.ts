import { z } from "zod";

// Schema for localized text content
const localizedTextSchema = z
  .object({
    en: z.string().optional().describe("English text content"),
    hi: z.string().optional().describe("Hindi text content"),
    // Add other languages as needed
  })
  .describe("Multilingual text content");

// Schema for observation item
const observationItemSchema = z
  .object({
    obs_name: z
      .string()
      .nullable()
      .optional()
      .describe("Name of the observation (e.g., Temperature, Heart Rate)"),
    obs_val: z
      .string()
      .nullable()
      .optional()
      .describe("Value of the observation with units (e.g., 39.5°C, 75 bpm)"),
  })
  .describe("Single observation record with name and value");

// Schema for observation notes
const observationNoteSchema = z
  .object({
    note: z
      .union([z.string(), localizedTextSchema])
      .describe("Observation note text, can be plain text or localized"),
  })
  .describe("Note about observations made during examination");

// Schema for diagnosis item
const diagnosisItemSchema = z
  .object({
    diag_name: z
      .string()
      .nullable()
      .optional()
      .describe("Name of the diagnosed condition"),
  })
  .describe("Single diagnosis record");

// Schema for prescription details
const prescriptionDetailsSchema = z
  .object({
    unit: z
      .string()
      .nullable()
      .optional()
      .describe("Unit of measurement for the medicine (e.g., ml, mg)"),
    frequency: z
      .string()
      .nullable()
      .optional()
      .describe(
        "How often the medicine should be administered (e.g., Once daily)"
      ),
    route_name: z
      .string()
      .nullable()
      .optional()
      .describe("Route of administration (e.g., Oral, Intramuscular)"),
    mrp: z
      .string()
      .nullable()
      .optional()
      .describe("Maximum retail price of the medicine"),
  })
  .describe("Details about the prescribed medicine");

// Schema for prescription item
const prescriptionItemSchema = z
  .object({
    prescription_name: z
      .string()
      .nullable()
      .optional()
      .describe("Name of the prescribed medicine"),
    prescription_value: z
      .string()
      .nullable()
      .optional()
      .describe("Dosage amount of the medicine"),
    prescription_details: prescriptionDetailsSchema
      .nullable()
      .optional()
      .describe("Additional details about the prescription"),
  })
  .describe(
    "Single prescription record with medicine name, dosage, and details"
  );

// Schema for prescription notes
const prescriptionNoteSchema = z
  .object({
    note: z
      .union([z.string(), localizedTextSchema])
      .describe("Prescription advisory note, can be plain text or localized"),
  })
  .describe("Advisory note for the prescription");

// Schema for response to treatment
const responseToTreatmentSchema = z
  .object({
    rtt_name: z
      .string()
      .optional()
      .describe("Response to treatment description (e.g., Positive, Negative)"),
  })
  .describe("Record of how the animal responded to treatment");

// Main activity details schema
export const activityDetailsSchema = z
  .object({
    // Basic ticket information
    ticket_number: z
      .string()
      .nullable()
      .optional()
      .describe("Unique identifier for the complaint/consultation"),
    activity_date: z
      .string()
      .nullable()
      .optional()
      .describe("Date and time of the activity in ISO format"),

    // Farmer information
    customer_name_l10n: localizedTextSchema
      .nullable()
      .optional()
      .describe("Farmer's name in multiple languages"),
    farmer_contact: z
      .string()
      .nullable()
      .optional()
      .describe("Farmer's phone number"),
    customer_visual_id: z
      .string()
      .nullable()
      .optional()
      .describe("Farmer's ID number"),

    // Animal information
    animal_name: z
      .string()
      .nullable()
      .optional()
      .describe("Name of the animal"),
    ear_tag: z
      .string()
      .nullable()
      .optional()
      .describe("Tag number attached to the animal's ear"),
    animal_visual_id: z
      .string()
      .nullable()
      .optional()
      .describe("Reference number for the animal"),

    // Location information
    village_name_l10n: localizedTextSchema
      .nullable()
      .optional()
      .describe("Village name in multiple languages"),

    // Veterinary staff information
    paravet_name: z
      .union([z.string(), localizedTextSchema])
      .nullable()
      .optional()
      .describe("Name of the livestock service specialist"),
    vet_name: z
      .union([z.string(), localizedTextSchema])
      .nullable()
      .optional()
      .describe("Name of the veterinary officer"),

    // Activity information
    activity_name_l10n: localizedTextSchema
      .nullable()
      .optional()
      .describe("Type of activity or complaint in multiple languages"),
    followUpDateTime: z
      .string()
      .nullable()
      .optional()
      .describe("Date and time for follow-up in ISO format"),

    // Treatment response
    response_to_treatment: z
      .array(responseToTreatmentSchema)
      .nullable()
      .optional()
      .describe("Records of how the animal responded to treatment"),

    // Clinical notes and observations
    observationNotes: z
      .array(observationNoteSchema)
      .nullable()
      .optional()
      .describe("Notes about the case and observations"),
    observation: z
      .array(observationItemSchema)
      .nullable()
      .optional()
      .describe("Structured observations with values"),

    // Diagnosis and prescription
    diagnosis: z
      .array(diagnosisItemSchema)
      .nullable()
      .optional()
      .describe("List of diagnosed conditions"),
    prescription: z
      .array(prescriptionItemSchema)
      .nullable()
      .optional()
      .describe("List of prescribed medicines"),
    prescriptionNotes: z
      .array(prescriptionNoteSchema)
      .nullable()
      .optional()
      .describe("Advisory notes for the prescription"),

    // Financial information
    vet_consultant_cost: z
      .string()
      .nullable()
      .optional()
      .describe("Consultation fee charged by the veterinarian"),
    total_price: z
      .number()
      .nullable()
      .optional()
      .describe(
        "Total cost of the prescription including medicines and consultation"
      ),
  })
  .describe("Complete details of a veterinary activity or consultation");

// Type definition derived from the schema
export type ActivityDetails = z.infer<typeof activityDetailsSchema>;

// Example usage:
// const validatedData = activityDetailsSchema.parse(rawData);
// const isValid = activityDetailsSchema.safeParse(rawData).success;
