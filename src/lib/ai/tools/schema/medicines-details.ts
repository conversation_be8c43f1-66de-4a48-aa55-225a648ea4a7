import { z } from "zod";

export const medicinesDetailsSchema = z.object({
  activity_id: z
    .string()
    .describe(
      "activity id is care calendar id. get medicines id array from the user input "
    )
    .min(1, "activity_id is required"),
  medicine: z
    .array(
      z.object({
        id: z
          .union([z.string().min(1, "medicine id is required"), z.number()])
          .describe("medicine id"),
        name: z.string().optional().describe("medicine name is required"),
        dosage: z.number().optional().describe("dosage of the medicine"),
        prescribed_info: z
          .object({
            unit: z.string().describe("unit of the medicine dosage"),
            route: z.number().describe("route of the medicine"),
            frequency: z.string().describe("frequency of the medicine"),
            status: z.string().describe("status of the medicine"),
          })
          .optional(),
      })
    )
    .min(1, "At least one medicine is required")
    .describe("Array of medicine objects to update"),
  follow_up_required: z
    .boolean()
    .describe("Indicates if a follow-up is required"),
  vet_cosultant_type: z
    .array(z.union([z.string().min(1, "medicine id is required"), z.number()]))
    .optional()
    .describe("Type of vet consultant for the medicine"),
});
