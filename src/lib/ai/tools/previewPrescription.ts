import { tool } from "ai";
import { z } from "zod";
import { medicinesDetailsSchema } from "./schema/medicines-details";
import { diagnosisDetailsSchema } from "./schema/diagnosis-details";
import { activityDetailsSchema } from "./schema/activity-details";
import { updateDiagnosis } from "@/lib/services/diagnosis";
import { updateMedicines } from "@/lib/services/medicines";

export const previewPrescription = tool({
  description:
    "preview prescription for a bovine based on details but you need observation, diagnosis and medication before previewing",
  parameters: z.object({
    complaintDetails: activityDetailsSchema,
    observation: z.object({}).describe("The observation of the bovine"),
    diagnosis: diagnosisDetailsSchema,
    medication: medicinesDetailsSchema,
  }),
  execute: async ({ complaintDetails, observation, diagnosis, medication }) => {
    console.log(complaintDetails, diagnosis, medication, observation);
    if (!diagnosis) {
      return "Required Diagnosis details,  ask for diagnosis";
    }

    if (!medication) {
      return "Required Medication details,  ask for medication";
    }

    return "all details are available for preview";
  },
});
