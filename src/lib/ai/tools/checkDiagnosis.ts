import { tool } from "ai";
import { z } from "zod";
import Fuse from "fuse.js";
import { diagnosisList } from "@/lib/services/diagnosis";

type Diagnosis = {
  id: string;
  name: string;
};
import { RequestOptions } from "@/types/RequestOption";

// Initialize Fuse with options for better fuzzy searching
const fuseOptions = {
  keys: ["name", "id"],
  includeScore: true,
  exactMatch: true,
  threshold: 0.1, // Lower threshold means more strict matching
  minMatchCharLength: 4,
};

export const checkDiagnosis = (options: RequestOptions) =>
  tool({
    description: "tool to check if diagnosis is available in the list",
    parameters: z.object({
      diagnosis: z
        .string()
        .describe("diagnosis name is required to check with the list"),
      careCalendarId: z
        .string()
        .describe(
          "care calendar id is required to get the diagnosis list. this is available from tool response getComplaintDetailsById"
        ),
    }),
    execute: async ({ diagnosis, careCalendarId }) => {
      const diagnosisListArray: Diagnosis[] = await diagnosisList(
        careCalendarId,
        options
      );

      const fuse = new Fuse(diagnosisListArray, fuseOptions);

      if (
        !diagnosis ||
        diagnosis.trim() === "" ||
        !careCalendarId ||
        careCalendarId.trim() === ""
      ) {
        return {
          found: false,
          message:
            "Please provide a diagnosis and care calendar id to search medicine from the list",
          matches: [],
        };
      }

      // Search for matches using Fuse.js
      const searchResults = fuse.search(diagnosis);

      // Format results
      const matches = searchResults.map((result) => ({
        id: result?.item?.id,
        name: result?.item?.name,
        score: result.score,
      }));

      if (matches.length === 0) {
        return {
          found: false,
          message: "No matching diagnosis found, please try again",
          matches: [],
        };
      }

      console.log(matches.slice(0, 4));

      return {
        found: matches.length > 0,
        message:
          matches.length > 0
            ? `Found ${matches.length} matching diagnosis`
            : "No matching diagnosis found",
        matches: matches.slice(0, 4),
        exactMatch:
          matches.length > 0 &&
          matches[0]?.score !== undefined &&
          matches[0].score < 0.1, // Consider a very close match as exact
      };
    },
  });

// export const updateDiagnosisTool = tool({
//   description:
//     "This tool should execute after the comfirmToDownloadPDF tool execution , if user confirmed ,to update the diagnosis information.",
//   parameters: z.object({
//     activity_id: z
//       .string()
//       .describe(
//         "activity id is care calendar id  . get diagnosis id array from the user input "
//       )
//       .min(1, "activity_id is required"),
//     diagnosis: z
//       .array(
//         z.object({
//           value: z
//             .string()
//             .describe("diagnosis id is required")
//             .min(1, "diagnosis id is required"),
//         })
//       )
//       .min(1, "diagnosis is required")
//       .describe("diagnosis is required"),
//   }),
//   execute: async (updateDiagnosisParams: updateDiagnosisParams) => {
//     console.log("updateDiagnosisParams", updateDiagnosisParams);
//     await updateDiagnosis(updateDiagnosisParams);
//     return {
//       message: "updated",
//     };
//   },
// });
