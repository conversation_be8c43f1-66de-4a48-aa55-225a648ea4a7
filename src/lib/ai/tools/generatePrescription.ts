import { tool } from "ai";
import { z } from "zod";
import { medicinesDetailsSchema } from "./schema/medicines-details";
import { diagnosisDetailsSchema } from "./schema/diagnosis-details";

import { updateDiagnosis } from "@/lib/services/diagnosis";
import { updateMedicines } from "@/lib/services/medicines";
import { RequestOptions } from "@/types/RequestOption";

export const generatePrescription = (options: RequestOptions) =>
  tool({
    description:
      "Create prescription for a bovine based on details but you need observation, diagnosis and medication before generating",
    parameters: z.object({
      // complaintDetails: activityDetailsSchema,
      // observation: z.object({}).describe("The observation of the bovine"),
      diagnosis: diagnosisDetailsSchema,
      medication: medicinesDetailsSchema,
      downlaodPDF: z.object({
        farmerId: z.string().describe("farmer id is required"),
        careCalendarId: z.string().describe("activity id is required"),
      }),
    }),
    execute: async ({
      // complaintDetails,
      // observation,
      diagnosis,
      medication,
      downlaodPDF,
    }) => {
      if (!diagnosis) {
        return "Required Diagnosis details,  ask for diagnosis";
      }

      if (!medication) {
        return "Required Medication details,  ask for medication";
      }

      try {
        // clean up old stuff diagnosis and medicines,
        await updateDiagnosis(
          {
            activity_id: diagnosis.activity_id,
            diagnosis: [],
          },
          options
        );
        await updateMedicines(
          {
            activity_id: medication.activity_id,
            medicine: [],
            follow_up_required: false,
            vet_cosultant_type: [1000750001],
          },
          options
        );
        console.log("clear");
        await updateDiagnosis(diagnosis, options);
        console.log("Updated Diagnosis");

        await updateMedicines(medication, options);
        console.log("Updated Medication");
        /* call generate prescription here with the required details 
      once completed and successfully generated return the pdf url 
      return {
        message: "Prescription completed and generated",
        url: "https://example.com/pdf",
      }
      */
        return {
          message: "Prescription completed and generated",
          data: downlaodPDF,
        };
        // return "success";
      } catch (err) {
        return "Error in updating diagnosis";
      }
    },
  });
