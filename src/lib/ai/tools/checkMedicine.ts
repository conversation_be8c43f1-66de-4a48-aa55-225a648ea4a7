import { tool } from "ai";
import { z } from "zod";
import Fuse from "fuse.js";
import {
  medicineList,
  updateMedicines,
  updateMedicinesParams,
} from "@/lib/services/medicines";

type Medicine = {
  id: string;
  name: string;
};
import { RequestOptions } from "@/types/RequestOption";

// Initialize Fuse with options for better fuzzy searching
const fuseOptions = {
  keys: ["name", "id"],
  includeScore: true,
  threshold: 0.4, // Lower threshold means more strict matching
  minMatchCharLength: 3,
};

// const fuse = await initializeFuse();

export const checkMedicine = (options: RequestOptions) =>
  tool({
    description: "tool to check if medicine is available in the list",
    parameters: z.object({
      medicines: z
        .array(
          z.object({
            name: z.string().describe("name of the medicine is needed"),
            dose: z.string().describe("dose of the medicine is needed"),
            unit: z.string().describe("unit of the medicine dosage is needed"),
            route: z.string().describe("route of the medicine is needed"),
            frequency: z
              .string()
              .describe("frequency of the medicine is needed"),
          })
        )
        .describe("at least 1 medicine is required to check with the list"),
    }),
    execute: async ({ medicines }) => {
      console.log("medicines: ", medicines);
      if (medicines.length <= 0) {
        return {
          found: false,
          message: "At least 1 medicine is needed to check with the list",
          matches: [],
        };
      }
      const medicineListArray: Medicine[] = await medicineList(options);
      // console.log("medicineListArray", medicineListArray);
      const fuse = new Fuse(medicineListArray, fuseOptions);
      // console.log("fuse", fuse);
      // Process each medicine name for matching
      const results = [];

      for (const med of medicines) {
        // Search for matches using Fuse.js
        const searchResults = fuse.search(med.name);
        // console.log("searchResults", searchResults);
        // Format results for this medicine
        const matches = searchResults.map((result) => ({
          id: result?.item?.id,
          name: result?.item?.name,
          score: result.score,
          requestedDose: med.dose,
          requestedFrequency: med.frequency,
        }));

        console.log("matches", matches);

        results.push({
          searchedFor: med.name,
          matches: matches.slice(0, 4),
          found: matches.length > 0,
          exactMatch:
            matches.length > 0 &&
            matches[0]?.score !== undefined &&
            matches[0].score < 0.1,
        });
      }
      console.log("RESULT", results);
      return {
        found: results.some((result) => result.found),
        message: results.some((result) => result.found)
          ? `Found matches for some or all of the requested medicines`
          : "No matching medicines found in our database",
        results: results,
      };
    },
  });

// export const updateMedicinesTool = tool({
//   description:
//     "tool to update medicine. this should be called once medicines is talken from user and is confirmed by the user",
//   parameters: z.object({
//     activity_id: z
//       .string()
//       .describe(
//         "activity id is care calendar id. get medicines id array from the user input "
//       )
//       .min(1, "activity_id is required"),
//     medicine: z
//       .array(
//         z.object({
//           id: z.string().optional().describe("medicine id is required"),
//           dosage: z.number().optional().describe("dosage of the medicine"),
//           prescribed_info: z
//             .object({
//               unit: z.string().describe("unit of the medicine dosage"),
//               route: z.number().describe("route of the medicine"),
//               frequency: z.string().describe("frequency of the medicine"),
//               status: z.string().describe("status of the medicine"),
//             })
//             .optional(),
//         })
//       )
//       .min(1, "At least one medicine is required")
//       .describe("Array of medicine objects to update"),
//     follow_up_required: z
//       .boolean()
//       .describe("Indicates if a follow-up is required"),
//     vet_cosultant_type: z
//       .number()
//       .describe("Type of vet consultant for the medicine"),
//   }),
//   execute: async (updateDiagnosisParams: updateMedicinesParams) => {
//     await updateMedicines(updateDiagnosisParams);
//     return {
//       message: "updated",
//     };
//   },
// });
