import {
  getAdditionalTaskDetails,
  getTaskBySrNumber,
} from "@/lib/services/task";
import { RequestOptions } from "@/types/RequestOption";
import { tool } from "ai";
import { z } from "zod";

export const getComplaintDetailsById = (options: RequestOptions) =>
  tool({
    description: "tool to get complaint details by id",
    parameters: z.object({
      complaintNumber: z
        .string()
        .describe("The complaint Number of the bovine"),
    }),
    execute: async ({ complaintNumber }) => {
      const complaintDetailsResponse = await getTaskBySrNumber(
        complaintNumber,
        options
      );
      if (complaintDetailsResponse instanceof Error) {
        return {
          error:
            "Complaint not found, please check the complaint number and try again",
        };
      }
      if (complaintDetailsResponse.error) {
        return {
          error: complaintDetailsResponse,
        };
      }
      const complaintDetails = complaintDetailsResponse?.filter(
        (complaint: any) => complaint.complaint_number === complaintNumber
      )?.[0];

      console.log("complaintDetails", complaintDetails);

      if (!complaintDetails) {
        return {
          error:
            "Complaint not found, please check the complaint number and try again",
        };
      }
      const additionDetails = await getAdditionalTaskDetails(
        complaintDetails.care_calendar_id,
        options
      );
      delete additionDetails.diagnosis;
      delete additionDetails.observation_category_configuration;
      delete additionDetails.observation;
      delete additionDetails.prescriptionPDF;
      delete additionDetails.payment_detail_break_up;
      delete additionDetails.medicines;
      delete additionDetails.prescription;

      const sanitizedData = { ...complaintDetails, ...additionDetails };
      console.log(sanitizedData);
      return { ...sanitizedData };
    },
  });
