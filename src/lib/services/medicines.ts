import axios from "axios";
import { z } from "zod";
import { medicinesDetailsSchema } from "../ai/tools/schema/medicines-details";
import { RequestOptions } from "@/types/RequestOption";

const headers = {
  "Content-Type": "application/json",
  Token: process.env.Token || "",
};
export const medicineList = async (options: RequestOptions): Promise<any> => {
  try {
    const response = await axios.get(`${options.baseUrl}/medicine`, {
      headers: options.headers,
    });
    const medicineList = response.data;
    const medicineListArray = Object.keys(medicineList).map((key) => {
      const medicine = medicineList[key];
      return {
        id: medicine.medicine_id,
        name: medicine.medicine_name,
      };
    });
    return medicineListArray;
  } catch (error) {
    console.log("task list error", error);
    return { error };
  }
};

export type updateMedicinesParams = {
  activity_id: string;
  medicine: [
    {
      id: string;
      dosage: number;
      prescribed_info: {
        unit: string;
        route: number;
        frequency: string;
        status: string;
      };
    }
  ];
};

type medications = z.infer<typeof medicinesDetailsSchema>;

export const updateMedicines = async (
  params: medications,
  options: RequestOptions
) => {
  try {
    const getMedicinesByActivityId = await getMedicinesById(
      params.activity_id,
      options
    );
    console.log("getMedicinesById", getMedicinesById);
    const previousMedicines =
      getMedicinesByActivityId.length > 0 ? getMedicinesByActivityId : [];

    const payload = {
      activity_id: params.activity_id,
      medicine: [
        ...params.medicine.map((medicine: medications["medicine"][0]) => ({
          id: medicine.id,
          dosage: medicine.dosage ?? 0,
          prescribed_info: {
            unit: medicine.prescribed_info?.unit ?? "",
            route: 1009000003,
            frequency: medicine.prescribed_info?.frequency ?? "",
            status: "PUBLISHED",
          },
        })),
      ] as [updateMedicinesParams["medicine"][0]],
      follow_up_required: false,
      vet_cosultant_type: 1000750001,
    };

    console.log("medicine params", JSON.stringify(payload));

    fetch(`${options.baseUrl}/v2/tasks/prescription/publish`, {
      method: "POST",
      headers: options.headers,
      body: JSON.stringify(payload),
    })
      .then(async (res) => {
        if (!res.ok) {
          const text = await res.text();
          throw new Error(`Status ${res.status}: ${text}`);
        }
        return res.json();
      })
      .then((data) => console.log("Success:", data))
      .catch((err) => console.error("Error:", err.message));

    return "success";
  } catch (error) {
    console.log("updateMedicines error", error);
    return { error };
  }
};

export const getMedicinesById = async (
  activityId: string,
  options: RequestOptions
) => {
  try {
    const url = `${options.baseUrl}/activity/prescription/${activityId}`;

    const response = await axios.get(url, { headers: options.headers });

    return response.data?.data?.selected?.medicine;
  } catch (error) {
    console.log("getMedicinesById error", error);
    return { error };
  }
};

//{"activity_id":"02d8949a-86f6-11ef-b38e-6ba3c3897788","medicine":[],"follow_up_required":false,"vet_cosultant_type":1000750001}
