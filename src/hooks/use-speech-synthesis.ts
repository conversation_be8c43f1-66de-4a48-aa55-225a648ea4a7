"use client";

import { useState, useEffect, useRef, useCallback } from "react";

type SpeechState = "idle" | "speaking" | "paused" | "ended" | "processing";

interface UseSpeechSynthesisProps {
  onEnd?: () => void;
  onError?: (error: SpeechSynthesisErrorEvent | Error) => void;
  onBoundary?: (event: SpeechSynthesisEvent) => void;
  onApiStart?: () => void;
  onApiEnd?: () => void;
  filterLLMOutput?: boolean;
  skipTechnicalTerms?: boolean;
  apiEndpoint?: string;
}

interface UseSpeechSynthesisReturn {
  speak: (text: string) => void;
  speakRaw: (text: string) => void;
  speakWithAPI: (prompt: string) => Promise<void>;
  cancel: () => void;
  pause: () => void;
  resume: () => void;
  state: SpeechState;
  isSupported: boolean;
  voices: SpeechSynthesisVoice[];
  setCurrentVoice: (voice: SpeechSynthesisVoice) => void;
  setRate: (rate: number) => void;
  setPitch: (pitch: number) => void;
  setVolume: (volume: number) => void;
  currentVoice: SpeechSynthesisVoice | null;
  rate: number;
  pitch: number;
  volume: number;
  isSpeaking: boolean;
  isProcessing: boolean;
  toggleFiltering: () => void;
  isFilteringEnabled: boolean;
  lastApiResponse: string | null;
  apiError: string | null;
}

export function useSpeechSynthesis({
  onEnd,
  onError,
  onBoundary,
  onApiStart,
  onApiEnd,
  filterLLMOutput = true,
  skipTechnicalTerms = true,
  apiEndpoint = "/api/summarize",
}: UseSpeechSynthesisProps = {}): UseSpeechSynthesisReturn {
  const [state, setState] = useState<SpeechState>("idle");
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [currentVoice, setCurrentVoice] = useState<SpeechSynthesisVoice | null>(
    null
  );
  const [rate, setRate] = useState<number>(1);
  const [pitch, setPitch] = useState<number>(1);
  const [volume, setVolume] = useState<number>(1);
  const [isFilteringEnabled, setIsFilteringEnabled] =
    useState<boolean>(filterLLMOutput);
  const [lastApiResponse, setLastApiResponse] = useState<string | null>(null);
  const [apiError, setApiError] = useState<string | null>(null);

  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);
  const apiCallInProgressRef = useRef<boolean>(false);

  const isSupported =
    typeof window !== "undefined" && "speechSynthesis" in window;
  const isSpeaking = state === "speaking" || state === "paused";
  const isProcessing = state === "processing";

  // Text filtering function
  const filterText = useCallback(
    (text: string): string => {
      if (!isFilteringEnabled) return text;

      let filtered = text;

      if (skipTechnicalTerms) {
        filtered = filtered
          .replace(/```[\s\S]*?```/g, " [code block] ") // Code blocks
          .replace(/`[^`]+`/g, " [code] ") // Inline code
          .replace(/https?:\/\/[^\s]+/g, " [link] ") // URLs
          .replace(/\b[A-Z_]{3,}\b/g, " [constant] ") // ALL_CAPS constants
          .replace(/\b\w+\.\w+\(/g, " [function call] ") // Function calls
          .replace(/[{}[\]()]/g, " ") // Remove brackets and parentheses
          .replace(/\s+/g, " ") // Normalize whitespace
          .trim();
      }

      return filtered;
    },
    [isFilteringEnabled, skipTechnicalTerms]
  );

  // API call function
  const callSummarizeAPI = useCallback(
    async (prompt: string): Promise<string> => {
      try {
        setApiError(null);

        const response = await fetch(apiEndpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ prompt }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            errorData.error || `HTTP error! status: ${response.status}`
          );
        }

        const data = await response.json();

        if (!data.response) {
          throw new Error("Invalid response format from API");
        }

        setLastApiResponse(data.response);
        return data.response;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error occurred";
        setApiError(errorMessage);
        console.error("API call failed:", error);
        throw error;
      }
    },
    [apiEndpoint]
  );

  // Simple speak function
  const speakText = useCallback(
    (text: string) => {
      if (!isSupported) {
        console.error("Speech synthesis not supported");
        return;
      }

      if (!text?.trim()) {
        console.error("Empty text provided");
        return;
      }

      // Cancel any existing speech
      window.speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);
      utteranceRef.current = utterance;

      // Configure utterance
      if (currentVoice) utterance.voice = currentVoice;
      utterance.rate = Math.max(0.1, Math.min(10, rate));
      utterance.pitch = Math.max(0, Math.min(2, pitch));
      utterance.volume = Math.max(0, Math.min(1, volume));

      // Event handlers
      utterance.onstart = () => {
        console.log("Speech started");
        setState("speaking");
      };

      utterance.onend = () => {
        console.log("Speech ended");
        setState("ended");
        if (onEnd) onEnd();
      };

      utterance.onerror = (event) => {
        console.error("Speech error:", event.error);
        setState("idle");
        if (onError) onError(event);
      };

      if (onBoundary) {
        utterance.onboundary = onBoundary;
      }

      // Start speaking
      window.speechSynthesis.speak(utterance);
    },
    [isSupported, currentVoice, rate, pitch, volume, onEnd, onError, onBoundary]
  );

  // Main speak function with filtering
  const speak = useCallback(
    (text: string) => {
      const filteredText = filterText(text);

      if (!filteredText.trim()) {
        console.log("Text was filtered out completely, skipping speech");
        return;
      }

      console.log(
        "Speaking filtered text:",
        filteredText.substring(0, 100) + "..."
      );
      speakText(filteredText);
    },
    [filterText, speakText]
  );

  // Raw speak function without filtering
  const speakRaw = useCallback(
    (text: string) => {
      console.log("Speaking raw text:", text.substring(0, 100) + "...");
      speakText(text);
    },
    [speakText]
  );

  // Speak with API integration
  const speakWithAPI = useCallback(
    async (prompt: string): Promise<void> => {
      if (!isSupported) {
        const error = new Error("Speech synthesis not supported");
        if (onError) onError(error);
        throw error;
      }

      if (!prompt?.trim()) {
        const error = new Error("Empty prompt provided");
        if (onError) onError(error);
        throw error;
      }

      // Prevent multiple concurrent API calls
      if (apiCallInProgressRef.current) {
        console.log("API call already in progress, skipping");
        return;
      }

      try {
        apiCallInProgressRef.current = true;

        // Cancel any existing speech
        window.speechSynthesis.cancel();
        setState("processing");

        if (onApiStart) onApiStart();

        console.log(
          "Calling API with prompt:",
          prompt.substring(0, 100) + "..."
        );

        const apiResponse = await callSummarizeAPI(prompt);

        if (onApiEnd) onApiEnd();
        console.log(
          "API response received:",
          apiResponse.substring(0, 100) + "..."
        );

        const filteredText = filterText(apiResponse);

        if (!filteredText.trim()) {
          console.log(
            "API response was filtered out completely, skipping speech"
          );
          setState("idle");
          return;
        }

        console.log("Starting speech synthesis for API response");
        speakText(filteredText);
      } catch (error) {
        console.error("Error in speakWithAPI:", error);
        setState("idle");
        if (onError)
          onError(error instanceof Error ? error : new Error("Unknown error"));
        throw error;
      } finally {
        apiCallInProgressRef.current = false;
      }
    },
    [
      isSupported,
      callSummarizeAPI,
      onApiStart,
      onApiEnd,
      filterText,
      speakText,
      onError,
    ]
  );

  // Control functions
  const cancel = useCallback(() => {
    if (!isSupported) return;

    console.log("Canceling speech synthesis");
    window.speechSynthesis.cancel();
    setState("idle");
    utteranceRef.current = null;
  }, [isSupported]);

  const pause = useCallback(() => {
    if (!isSupported || !isSpeaking) return;
    window.speechSynthesis.pause();
    setState("paused");
  }, [isSupported, isSpeaking]);

  const resume = useCallback(() => {
    if (!isSupported || state !== "paused") return;
    window.speechSynthesis.resume();
    setState("speaking");
  }, [isSupported, state]);

  const toggleFiltering = useCallback(() => {
    setIsFilteringEnabled((prev) => !prev);
  }, []);

  // Voice management
  useEffect(() => {
    if (!isSupported) return;

    const updateVoices = () => {
      const availableVoices = window.speechSynthesis.getVoices();
      setVoices(availableVoices);

      if (availableVoices.length > 0 && !currentVoice) {
        const englishVoice = availableVoices.find((v) =>
          v.lang.startsWith("en-US")
        );
        const defaultVoice = availableVoices.find((v) => v.default);
        setCurrentVoice(englishVoice || defaultVoice || availableVoices[0]);
      }
    };

    const handleVoicesChanged = () => {
      updateVoices();
    };

    if ("onvoiceschanged" in window.speechSynthesis) {
      window.speechSynthesis.addEventListener(
        "voiceschanged",
        handleVoicesChanged
      );
    }

    updateVoices();

    return () => {
      if ("onvoiceschanged" in window.speechSynthesis) {
        window.speechSynthesis.removeEventListener(
          "voiceschanged",
          handleVoicesChanged
        );
      }
    };
  }, [isSupported, currentVoice]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isSupported) {
        window.speechSynthesis.cancel();
      }
    };
  }, [isSupported]);

  return {
    speak,
    speakRaw,
    speakWithAPI,
    cancel,
    pause,
    resume,
    state,
    isSupported,
    voices,
    setCurrentVoice,
    setRate,
    setPitch,
    setVolume,
    currentVoice,
    rate,
    pitch,
    volume,
    isSpeaking,
    isProcessing,
    toggleFiltering,
    isFilteringEnabled,
    lastApiResponse,
    apiError,
  };
}
