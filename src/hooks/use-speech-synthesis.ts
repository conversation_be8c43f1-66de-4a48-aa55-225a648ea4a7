"use client";

import { useState, useEffect, useRef, useCallback } from "react";

type SpeechState = "idle" | "speaking" | "paused" | "ended" | "processing";

interface UseSpeechSynthesisProps {
  onEnd?: () => void;
  onError?: (error: SpeechSynthesisErrorEvent | Error) => void;
  onBoundary?: (event: SpeechSynthesisEvent) => void;
  onApiStart?: () => void;
  onApiEnd?: () => void;
  filterLLMOutput?: boolean;
  skipTechnicalTerms?: boolean;
  apiEndpoint?: string;
}

interface UseSpeechSynthesisReturn {
  speak: (text: string) => Promise<void>;
  speakRaw: (text: string) => Promise<void>;
  speakWithAPI: (prompt: string) => Promise<void>;
  cancel: () => void;
  pause: () => void;
  resume: () => void;
  state: SpeechState;
  isSupported: boolean;
  setVolume: (volume: number) => void;
  volume: number;
  isSpeaking: boolean;
  isProcessing: boolean;
  toggleFiltering: () => void;
  isFilteringEnabled: boolean;
  lastApiResponse: string | null;
  apiError: string | null;
  ttsError: string | null;
}

export function useSpeechSynthesis({
  onEnd,
  onError,
  onBoundary,
  onApiStart,
  onApiEnd,
  filterLLMOutput = true,
  skipTechnicalTerms = true,
  apiEndpoint = "/api/summarize",
}: UseSpeechSynthesisProps = {}): UseSpeechSynthesisReturn {
  const [state, setState] = useState<SpeechState>("idle");
  const [volume, setVolume] = useState<number>(1);
  const [isFilteringEnabled, setIsFilteringEnabled] =
    useState<boolean>(filterLLMOutput);
  const [lastApiResponse, setLastApiResponse] = useState<string | null>(null);
  const [apiError, setApiError] = useState<string | null>(null);
  const [ttsError, setTtsError] = useState<string | null>(null);

  const audioRef = useRef<HTMLAudioElement | null>(null);
  const apiCallInProgressRef = useRef<boolean>(false);
  const lastTtsErrorRef = useRef<number>(0);

  const isSupported = typeof window !== "undefined";
  const isSpeaking = state === "speaking" || state === "paused";
  const isProcessing = state === "processing";

  // Text filtering function
  const filterText = useCallback(
    (text: string): string => {
      if (!isFilteringEnabled) return text;

      let filtered = text;

      if (skipTechnicalTerms) {
        filtered = filtered
          .replace(/```[\s\S]*?```/g, " [code block] ") // Code blocks
          .replace(/`[^`]+`/g, " [code] ") // Inline code
          .replace(/https?:\/\/[^\s]+/g, " [link] ") // URLs
          .replace(/\b[A-Z_]{3,}\b/g, " [constant] ") // ALL_CAPS constants
          .replace(/\b\w+\.\w+\(/g, " [function call] ") // Function calls
          .replace(/[{}[\]()]/g, " ") // Remove brackets and parentheses
          .replace(/\s+/g, " ") // Normalize whitespace
          .trim();
      }

      return filtered;
    },
    [isFilteringEnabled, skipTechnicalTerms]
  );

  // API call function
  const callSummarizeAPI = useCallback(
    async (prompt: string): Promise<string> => {
      try {
        setApiError(null);

        const response = await fetch(apiEndpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ prompt }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            errorData.error || `HTTP error! status: ${response.status}`
          );
        }

        const data = await response.json();

        if (!data.response) {
          throw new Error("Invalid response format from API");
        }

        setLastApiResponse(data.response);
        return data.response;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error occurred";
        setApiError(errorMessage);
        console.error("API call failed:", error);
        throw error;
      }
    },
    [apiEndpoint]
  );

  // API-based TTS function
  const speakTextWithAPI = useCallback(
    async (text: string): Promise<void> => {
      if (!isSupported) {
        const error = new Error("TTS not supported");
        console.error(error.message);
        if (onError) onError(error);
        throw error;
      }

      if (!text?.trim()) {
        const error = new Error("Empty text provided");
        console.error(error.message);
        if (onError) onError(error);
        throw error;
      }

      // Check if we had a recent TTS error (cooldown period of 30 seconds)
      const now = Date.now();
      if (now - lastTtsErrorRef.current < 30000) {
        console.log("TTS cooldown active, skipping TTS call");
        return;
      }

      try {
        setTtsError(null);
        setState("processing");

        // Call our TTS API
        const response = await fetch("/api/text-to-speech", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ text }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            errorData.error || `HTTP error! status: ${response.status}`
          );
        }

        const data = await response.json();

        if (!data.success || !data.audio) {
          throw new Error("Invalid response from TTS API");
        }

        // Handle audio playback
        // The API response should contain audio data or URL
        await playAudio(data.audio);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown TTS error";
        setTtsError(errorMessage);
        lastTtsErrorRef.current = Date.now(); // Set cooldown timestamp
        console.error("TTS error:", error);

        // Fallback to browser speech synthesis if available
        if (typeof window !== "undefined" && "speechSynthesis" in window) {
          console.log("Falling back to browser speech synthesis");
          try {
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.volume = Math.max(0, Math.min(1, volume));

            utterance.onstart = () => {
              setState("speaking");
            };

            utterance.onend = () => {
              setState("ended");
              if (onEnd) onEnd();
            };

            utterance.onerror = (event) => {
              console.error("Fallback speech error:", event.error);
              setState("idle");
            };

            window.speechSynthesis.speak(utterance);
            return; // Don't throw error if fallback works
          } catch (fallbackError) {
            console.error("Fallback speech synthesis failed:", fallbackError);
          }
        }

        setState("idle");
        if (onError)
          onError(error instanceof Error ? error : new Error(errorMessage));
        throw error;
      }
    },
    [isSupported, volume, onEnd, onError]
  );

  // Audio playback function
  const playAudio = useCallback(
    async (audioData: any): Promise<void> => {
      return new Promise((resolve, reject) => {
        try {
          // Cancel any existing audio
          if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current = null;
          }

          // Create new audio element
          const audio = new Audio();
          audioRef.current = audio;

          // Set volume
          audio.volume = Math.max(0, Math.min(1, volume));

          // Event handlers
          audio.onloadstart = () => {
            console.log("Audio loading started");
          };

          audio.oncanplay = () => {
            console.log("Audio can start playing");
            setState("speaking");
          };

          audio.onended = () => {
            console.log("Audio ended");
            setState("ended");
            if (onEnd) onEnd();
            resolve();
          };

          audio.onerror = (event) => {
            console.error("Audio error:", event);
            setState("idle");
            const error = new Error("Audio playback failed");
            if (onError) onError(error);
            reject(error);
          };

          // Handle different audio data formats
          if (typeof audioData === "string") {
            // If it's a URL or base64 string
            audio.src = audioData;
          } else if (audioData.url) {
            // If it's an object with URL
            audio.src = audioData.url;
          } else if (audioData.data) {
            // If it's base64 data
            audio.src = `data:audio/wav;base64,${audioData.data}`;
          } else {
            throw new Error("Unsupported audio data format");
          }

          // Start playing
          audio.play().catch((error) => {
            console.error("Failed to play audio:", error);
            setState("idle");
            if (onError) onError(error);
            reject(error);
          });
        } catch (error) {
          console.error("Error setting up audio:", error);
          setState("idle");
          if (onError)
            onError(
              error instanceof Error ? error : new Error("Audio setup failed")
            );
          reject(error);
        }
      });
    },
    [volume, onEnd, onError]
  );

  // Main speak function with filtering
  const speak = useCallback(
    async (text: string): Promise<void> => {
      const filteredText = filterText(text);

      if (!filteredText.trim()) {
        console.log("Text was filtered out completely, skipping speech");
        return;
      }

      console.log(
        "Speaking filtered text:",
        filteredText.substring(0, 100) + "..."
      );
      await speakTextWithAPI(filteredText);
    },
    [filterText, speakTextWithAPI]
  );

  // Raw speak function without filtering
  const speakRaw = useCallback(
    async (text: string): Promise<void> => {
      console.log("Speaking raw text:", text.substring(0, 100) + "...");
      await speakTextWithAPI(text);
    },
    [speakTextWithAPI]
  );

  // Speak with API integration
  const speakWithAPI = useCallback(
    async (prompt: string): Promise<void> => {
      if (!isSupported) {
        const error = new Error("TTS not supported");
        if (onError) onError(error);
        throw error;
      }

      if (!prompt?.trim()) {
        const error = new Error("Empty prompt provided");
        if (onError) onError(error);
        throw error;
      }

      // Prevent multiple concurrent API calls
      if (apiCallInProgressRef.current) {
        console.log("API call already in progress, skipping");
        return;
      }

      try {
        apiCallInProgressRef.current = true;

        // Cancel any existing audio
        if (audioRef.current) {
          audioRef.current.pause();
          audioRef.current = null;
        }
        setState("processing");

        if (onApiStart) onApiStart();

        console.log(
          "Calling API with prompt:",
          prompt.substring(0, 100) + "..."
        );

        const apiResponse = await callSummarizeAPI(prompt);

        if (onApiEnd) onApiEnd();
        console.log(
          "API response received:",
          apiResponse.substring(0, 100) + "..."
        );

        const filteredText = filterText(apiResponse);

        if (!filteredText.trim()) {
          console.log(
            "API response was filtered out completely, skipping speech"
          );
          setState("idle");
          return;
        }

        console.log("Starting TTS for API response");
        await speakTextWithAPI(filteredText);
      } catch (error) {
        console.error("Error in speakWithAPI:", error);
        setState("idle");
        if (onError)
          onError(error instanceof Error ? error : new Error("Unknown error"));
        // Don't re-throw the error to prevent loops
        return;
      } finally {
        apiCallInProgressRef.current = false;
      }
    },
    [
      isSupported,
      callSummarizeAPI,
      onApiStart,
      onApiEnd,
      filterText,
      speakTextWithAPI,
      onError,
    ]
  );

  // Control functions
  const cancel = useCallback(() => {
    if (!isSupported) return;

    console.log("Canceling audio playback");

    // Cancel HTML audio
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }

    // Cancel browser speech synthesis (fallback)
    if (typeof window !== "undefined" && "speechSynthesis" in window) {
      window.speechSynthesis.cancel();
    }

    setState("idle");
  }, [isSupported]);

  const pause = useCallback(() => {
    if (!isSupported || !audioRef.current) return;

    console.log("Pausing audio playback");
    audioRef.current.pause();
    setState("paused");
  }, [isSupported]);

  const resume = useCallback(() => {
    if (!isSupported || !audioRef.current) return;

    console.log("Resuming audio playback");
    audioRef.current.play().catch((error) => {
      console.error("Failed to resume audio:", error);
      setState("idle");
    });
    setState("speaking");
  }, [isSupported]);

  const toggleFiltering = useCallback(() => {
    setIsFilteringEnabled((prev) => !prev);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }

      // Cancel browser speech synthesis
      if (typeof window !== "undefined" && "speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  return {
    speak,
    speakRaw,
    speakWithAPI,
    cancel,
    pause,
    resume,
    state,
    isSupported,
    setVolume,
    volume,
    isSpeaking,
    isProcessing,
    toggleFiltering,
    isFilteringEnabled,
    lastApiResponse,
    apiError,
    ttsError,
  };
}
